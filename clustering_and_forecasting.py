import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, silhouette_score
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# Time series libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
import itertools
from datetime import datetime, timedelta
import os

# Set style for better plots
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

class StoreAnalytics:
    def __init__(self, csv_file):
        """Initialize the analytics class with data loading and preprocessing"""
        self.df = pd.read_csv(csv_file)
        self.prepare_data()
        self.output_dir = 'output/arima'
        os.makedirs(self.output_dir, exist_ok=True)

    def prepare_data(self):
        """Prepare and clean the data for analysis"""
        # Convert date column to datetime
        self.df['date'] = pd.to_datetime(self.df['date'])

        # Extract city from address for clustering
        self.df['city'] = self.df['address'].str.split(',').str[-1].str.strip()

        # Create store summary for clustering
        self.store_summary = self.df.groupby('businessName').agg({
            'businessType': 'first',
            'isDigital': 'first',
            'hasWebsite': 'first',
            'city': 'first',
            'sales': ['mean', 'std', 'sum'],
            'customers': ['mean', 'std', 'sum'],
            'promo': 'mean',  # Promotion frequency
            'isWeekend': 'mean'  # Weekend operation frequency
        }).round(2)

        # Flatten column names
        self.store_summary.columns = ['_'.join(col).strip() if col[1] else col[0]
                                     for col in self.store_summary.columns.values]
        self.store_summary.reset_index(inplace=True)

        print("Data prepared successfully!")
        print(f"Total stores: {self.df['businessName'].nunique()}")
        print(f"Date range: {self.df['date'].min()} to {self.df['date'].max()}")
        print(f"Total records: {len(self.df)}")

    def perform_clustering_analysis(self):
        """Perform comprehensive clustering analysis"""
        print("\n=== CLUSTERING ANALYSIS ===")

        # Prepare features for clustering
        features_for_clustering = self.store_summary.copy()

        # Encode categorical variables
        le_business = LabelEncoder()
        le_city = LabelEncoder()

        features_for_clustering['businessType_encoded'] = le_business.fit_transform(
            features_for_clustering['businessType_first'])
        features_for_clustering['city_encoded'] = le_city.fit_transform(
            features_for_clustering['city_first'])

        # Select numerical features for clustering
        clustering_features = [
            'businessType_encoded', 'isDigital_first', 'hasWebsite_first',
            'city_encoded', 'sales_mean', 'sales_std', 'customers_mean',
            'customers_std', 'promo_mean'
        ]

        X = features_for_clustering[clustering_features]

        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # Apply different clustering algorithms
        self._apply_kmeans_clustering(X_scaled, features_for_clustering)
        self._apply_hierarchical_clustering(X_scaled, features_for_clustering)
        self._apply_dbscan_clustering(X_scaled, features_for_clustering)

        # Decision Tree Analysis (ID3-like)
        self._decision_tree_analysis(features_for_clustering, clustering_features)

    def _apply_kmeans_clustering(self, X_scaled, features_df):
        """Apply K-Means clustering"""
        print("\n--- K-Means Clustering ---")

        # Find optimal number of clusters using elbow method
        inertias = []
        silhouette_scores = []
        k_range = range(2, 8)

        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(X_scaled)
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(X_scaled, kmeans.labels_))

        # Plot elbow curve
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        ax1.plot(k_range, inertias, 'bo-')
        ax1.set_xlabel('Number of Clusters (k)')
        ax1.set_ylabel('Inertia')
        ax1.set_title('Elbow Method for Optimal k')
        ax1.grid(True)

        ax2.plot(k_range, silhouette_scores, 'ro-')
        ax2.set_xlabel('Number of Clusters (k)')
        ax2.set_ylabel('Silhouette Score')
        ax2.set_title('Silhouette Score vs Number of Clusters')
        ax2.grid(True)

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/kmeans_optimization.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Use optimal k (let's use k=4 based on business types)
        optimal_k = 4
        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        features_df['kmeans_cluster'] = kmeans.fit_predict(X_scaled)

        # Visualize clusters using PCA
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)

        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1],
                            c=features_df['kmeans_cluster'],
                            cmap='viridis', s=100, alpha=0.7)

        # Add store names as labels
        for i, store in enumerate(features_df['businessName']):
            plt.annotate(store, (X_pca[i, 0], X_pca[i, 1]),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)

        plt.colorbar(scatter)
        plt.xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('K-Means Clustering Results (PCA Visualization)')
        plt.grid(True, alpha=0.3)
        plt.savefig(f'{self.output_dir}/kmeans_clusters_pca.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Analyze cluster characteristics
        self._analyze_cluster_characteristics(features_df, 'kmeans_cluster', 'K-Means')

    def _apply_hierarchical_clustering(self, X_scaled, features_df):
        """Apply Hierarchical clustering"""
        print("\n--- Hierarchical Clustering ---")

        # Apply hierarchical clustering
        hierarchical = AgglomerativeClustering(n_clusters=4, linkage='ward')
        features_df['hierarchical_cluster'] = hierarchical.fit_predict(X_scaled)

        # Visualize with PCA
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)

        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1],
                            c=features_df['hierarchical_cluster'],
                            cmap='plasma', s=100, alpha=0.7)

        for i, store in enumerate(features_df['businessName']):
            plt.annotate(store, (X_pca[i, 0], X_pca[i, 1]),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)

        plt.colorbar(scatter)
        plt.xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('Hierarchical Clustering Results (PCA Visualization)')
        plt.grid(True, alpha=0.3)
        plt.savefig(f'{self.output_dir}/hierarchical_clusters_pca.png', dpi=300, bbox_inches='tight')
        plt.show()

        self._analyze_cluster_characteristics(features_df, 'hierarchical_cluster', 'Hierarchical')

    def _apply_dbscan_clustering(self, X_scaled, features_df):
        """Apply DBSCAN clustering"""
        print("\n--- DBSCAN Clustering ---")

        # Apply DBSCAN
        dbscan = DBSCAN(eps=0.5, min_samples=2)
        features_df['dbscan_cluster'] = dbscan.fit_predict(X_scaled)

        # Visualize with PCA
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)

        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1],
                            c=features_df['dbscan_cluster'],
                            cmap='tab10', s=100, alpha=0.7)

        for i, store in enumerate(features_df['businessName']):
            plt.annotate(store, (X_pca[i, 0], X_pca[i, 1]),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)

        plt.colorbar(scatter)
        plt.xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('DBSCAN Clustering Results (PCA Visualization)')
        plt.grid(True, alpha=0.3)
        plt.savefig(f'{self.output_dir}/dbscan_clusters_pca.png', dpi=300, bbox_inches='tight')
        plt.show()

        self._analyze_cluster_characteristics(features_df, 'dbscan_cluster', 'DBSCAN')

    def _decision_tree_analysis(self, features_df, clustering_features):
        """Perform ID3-like decision tree analysis"""
        print("\n--- Decision Tree Analysis (ID3-like) ---")

        # Use business type as target for decision tree
        X = features_df[clustering_features]
        y = features_df['businessType_first']

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

        # Train decision tree
        dt = DecisionTreeClassifier(criterion='entropy', max_depth=4, random_state=42)
        dt.fit(X_train, y_train)

        # Plot decision tree
        plt.figure(figsize=(20, 12))
        plot_tree(dt, feature_names=clustering_features,
                 class_names=dt.classes_, filled=True, rounded=True, fontsize=10)
        plt.title('Decision Tree Analysis - Business Type Classification')
        plt.savefig(f'{self.output_dir}/decision_tree_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': clustering_features,
            'importance': dt.feature_importances_
        }).sort_values('importance', ascending=False)

        plt.figure(figsize=(10, 6))
        plt.barh(feature_importance['feature'], feature_importance['importance'])
        plt.title('Feature Importance in Business Type Classification')
        plt.xlabel('Importance Score')
        plt.ylabel('Features')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("Feature Importance:")
        print(feature_importance)

        # Predictions and classification report
        y_pred = dt.predict(X_test)
        print("\nClassification Report:")
        print(classification_report(y_test, y_pred))

    def _analyze_cluster_characteristics(self, features_df, cluster_col, method_name):
        """Analyze and visualize cluster characteristics"""
        print(f"\n--- {method_name} Cluster Analysis ---")

        # Group by cluster and analyze characteristics
        cluster_analysis = features_df.groupby(cluster_col).agg({
            'businessType_first': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            'isDigital_first': 'mean',
            'hasWebsite_first': 'mean',
            'sales_mean': 'mean',
            'customers_mean': 'mean',
            'businessName': 'count'
        }).round(2)

        cluster_analysis.columns = ['Dominant_Business_Type', 'Digital_Ratio', 'Website_Ratio',
                                   'Avg_Sales', 'Avg_Customers', 'Store_Count']

        print(f"\n{method_name} Cluster Characteristics:")
        print(cluster_analysis)

        # Visualize cluster characteristics
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Digital ratio by cluster
        cluster_analysis['Digital_Ratio'].plot(kind='bar', ax=axes[0,0], color='skyblue')
        axes[0,0].set_title('Digital Ratio by Cluster')
        axes[0,0].set_ylabel('Digital Ratio')

        # Website ratio by cluster
        cluster_analysis['Website_Ratio'].plot(kind='bar', ax=axes[0,1], color='lightcoral')
        axes[0,1].set_title('Website Ratio by Cluster')
        axes[0,1].set_ylabel('Website Ratio')

        # Average sales by cluster
        cluster_analysis['Avg_Sales'].plot(kind='bar', ax=axes[1,0], color='lightgreen')
        axes[1,0].set_title('Average Sales by Cluster')
        axes[1,0].set_ylabel('Average Sales')

        # Store count by cluster
        cluster_analysis['Store_Count'].plot(kind='bar', ax=axes[1,1], color='gold')
        axes[1,1].set_title('Store Count by Cluster')
        axes[1,1].set_ylabel('Number of Stores')

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/{method_name.lower()}_cluster_characteristics.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def perform_arima_forecasting(self):
        """Perform ARIMA, SARIMA, and SARIMAX forecasting for each store"""
        print("\n=== ARIMA FORECASTING ANALYSIS ===")

        stores = self.df['businessName'].unique()
        forecast_results = {}

        for store in stores:
            print(f"\n--- Forecasting for {store} ---")
            store_data = self.df[self.df['businessName'] == store].copy()
            store_data = store_data.sort_values('date')

            # Create time series
            ts_data = store_data.set_index('date')['sales'].resample('D').mean().ffill()

            # Perform forecasting
            results = self._forecast_store_sales(store, ts_data, store_data)
            forecast_results[store] = results

        # Create summary comparison
        self._create_forecast_summary(forecast_results)

        return forecast_results

    def _forecast_store_sales(self, store_name, ts_data, store_data):
        """Forecast sales for a single store using multiple ARIMA models"""

        # Check stationarity
        adf_result = adfuller(ts_data.dropna())
        is_stationary = adf_result[1] <= 0.05

        print(f"ADF Test p-value: {adf_result[1]:.4f} - {'Stationary' if is_stationary else 'Non-stationary'}")

        # Make data stationary if needed
        if not is_stationary:
            ts_diff = ts_data.diff().dropna()
            adf_diff = adfuller(ts_diff)
            print(f"After differencing - ADF p-value: {adf_diff[1]:.4f}")
        else:
            ts_diff = ts_data

        # Split data for training and testing
        train_size = int(len(ts_data) * 0.8)
        train_data = ts_data[:train_size]
        test_data = ts_data[train_size:]

        # Prepare external variables for SARIMAX
        exog_vars = store_data.set_index('date')[['promo', 'nationalHoliday', 'schoolHoliday',
                                                 'isWeekend', 'customers']].resample('D').mean().ffill()
        exog_train = exog_vars[:train_size]
        exog_test = exog_vars[train_size:]

        results = {}

        # 1. ARIMA Model
        try:
            arima_model = self._fit_arima_model(train_data, test_data)
            results['ARIMA'] = arima_model
        except Exception as e:
            print(f"ARIMA failed: {e}")
            results['ARIMA'] = None

        # 2. SARIMA Model
        try:
            sarima_model = self._fit_sarima_model(train_data, test_data)
            results['SARIMA'] = sarima_model
        except Exception as e:
            print(f"SARIMA failed: {e}")
            results['SARIMA'] = None

        # 3. SARIMAX Model
        try:
            sarimax_model = self._fit_sarimax_model(train_data, test_data, exog_train, exog_test)
            results['SARIMAX'] = sarimax_model
        except Exception as e:
            print(f"SARIMAX failed: {e}")
            results['SARIMAX'] = None

        # Create comprehensive plots
        self._plot_store_forecasts(store_name, ts_data, train_data, test_data, results)

        return results

    def _fit_arima_model(self, train_data, test_data):
        """Fit ARIMA model with automatic parameter selection"""

        # Grid search for best ARIMA parameters
        p_values = range(0, 4)
        d_values = range(0, 2)
        q_values = range(0, 4)

        best_aic = float('inf')
        best_params = None
        best_model = None

        for p, d, q in itertools.product(p_values, d_values, q_values):
            try:
                model = ARIMA(train_data, order=(p, d, q))
                fitted_model = model.fit()

                if fitted_model.aic < best_aic:
                    best_aic = fitted_model.aic
                    best_params = (p, d, q)
                    best_model = fitted_model

            except:
                continue

        if best_model is None:
            # Fallback to simple ARIMA(1,1,1)
            model = ARIMA(train_data, order=(1, 1, 1))
            best_model = model.fit()
            best_params = (1, 1, 1)

        # Generate forecasts
        forecast_steps = len(test_data)
        forecast = best_model.forecast(steps=forecast_steps)
        forecast_ci = best_model.get_forecast(steps=forecast_steps).conf_int()

        # Calculate metrics
        mse = np.mean((test_data - forecast) ** 2)
        mae = np.mean(np.abs(test_data - forecast))

        return {
            'model': best_model,
            'params': best_params,
            'forecast': forecast,
            'confidence_interval': forecast_ci,
            'mse': mse,
            'mae': mae,
            'aic': best_aic
        }

    def _fit_sarima_model(self, train_data, test_data):
        """Fit SARIMA model with seasonal components"""

        # Grid search for best SARIMA parameters
        p_values = range(0, 3)
        d_values = range(0, 2)
        q_values = range(0, 3)
        seasonal_periods = [7, 30]  # Weekly and monthly seasonality

        best_aic = float('inf')
        best_params = None
        best_model = None

        for p, d, q in itertools.product(p_values, d_values, q_values):
            for s in seasonal_periods:
                try:
                    model = SARIMAX(train_data, order=(p, d, q), seasonal_order=(1, 1, 1, s))
                    fitted_model = model.fit(disp=False)

                    if fitted_model.aic < best_aic:
                        best_aic = fitted_model.aic
                        best_params = ((p, d, q), (1, 1, 1, s))
                        best_model = fitted_model

                except:
                    continue

        if best_model is None:
            # Fallback to simple SARIMA
            model = SARIMAX(train_data, order=(1, 1, 1), seasonal_order=(1, 1, 1, 7))
            best_model = model.fit(disp=False)
            best_params = ((1, 1, 1), (1, 1, 1, 7))

        # Generate forecasts
        forecast_steps = len(test_data)
        forecast = best_model.forecast(steps=forecast_steps)
        forecast_ci = best_model.get_forecast(steps=forecast_steps).conf_int()

        # Calculate metrics
        mse = np.mean((test_data - forecast) ** 2)
        mae = np.mean(np.abs(test_data - forecast))

        return {
            'model': best_model,
            'params': best_params,
            'forecast': forecast,
            'confidence_interval': forecast_ci,
            'mse': mse,
            'mae': mae,
            'aic': best_aic
        }

    def _fit_sarimax_model(self, train_data, test_data, exog_train, exog_test):
        """Fit SARIMAX model with external variables"""

        # Use simpler parameter search for SARIMAX due to complexity
        try:
            model = SARIMAX(train_data, exog=exog_train, order=(1, 1, 1), seasonal_order=(1, 1, 1, 7))
            fitted_model = model.fit(disp=False)

            # Generate forecasts
            forecast_steps = len(test_data)
            forecast = fitted_model.forecast(steps=forecast_steps, exog=exog_test)
            forecast_ci = fitted_model.get_forecast(steps=forecast_steps, exog=exog_test).conf_int()

            # Calculate metrics
            mse = np.mean((test_data - forecast) ** 2)
            mae = np.mean(np.abs(test_data - forecast))

            return {
                'model': fitted_model,
                'params': ((1, 1, 1), (1, 1, 1, 7)),
                'forecast': forecast,
                'confidence_interval': forecast_ci,
                'mse': mse,
                'mae': mae,
                'aic': fitted_model.aic
            }

        except Exception as e:
            print(f"SARIMAX fitting failed: {e}")
            return None

    def _plot_store_forecasts(self, store_name, ts_data, train_data, test_data, results):
        """Create comprehensive forecast plots for a store"""

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle(f'Sales Forecasting Analysis - {store_name}', fontsize=16, fontweight='bold')

        # Plot 1: Time series decomposition
        try:
            decomposition = seasonal_decompose(ts_data.dropna(), model='additive', period=30)

            axes[0, 0].plot(decomposition.trend, label='Trend', color='blue')
            axes[0, 0].set_title('Trend Component')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

        except:
            axes[0, 0].plot(ts_data.index, ts_data.values)
            axes[0, 0].set_title('Original Time Series')
            axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: ACF and PACF
        try:
            plot_acf(ts_data.dropna(), ax=axes[0, 1], lags=40, alpha=0.05)
            axes[0, 1].set_title('Autocorrelation Function')
        except:
            axes[0, 1].text(0.5, 0.5, 'ACF plot failed', ha='center', va='center', transform=axes[0, 1].transAxes)

        # Plot 3: Model comparison
        axes[1, 0].plot(train_data.index, train_data.values, label='Training Data', color='blue', alpha=0.7)
        axes[1, 0].plot(test_data.index, test_data.values, label='Actual Test Data', color='black', linewidth=2)

        colors = ['red', 'green', 'orange']
        model_names = ['ARIMA', 'SARIMA', 'SARIMAX']

        for i, model_name in enumerate(model_names):
            if results.get(model_name) and results[model_name] is not None:
                forecast = results[model_name]['forecast']
                ci = results[model_name]['confidence_interval']

                axes[1, 0].plot(test_data.index, forecast, label=f'{model_name} Forecast',
                               color=colors[i], linewidth=2, linestyle='--')

                if hasattr(ci, 'iloc'):
                    axes[1, 0].fill_between(test_data.index,
                                          ci.iloc[:, 0], ci.iloc[:, 1],
                                          alpha=0.2, color=colors[i])

        axes[1, 0].set_title('Model Forecasts Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Model performance metrics
        model_metrics = []
        for model_name in model_names:
            if results.get(model_name) and results[model_name] is not None:
                model_metrics.append({
                    'Model': model_name,
                    'MSE': results[model_name]['mse'],
                    'MAE': results[model_name]['mae'],
                    'AIC': results[model_name]['aic']
                })

        if model_metrics:
            metrics_df = pd.DataFrame(model_metrics)

            # Plot MSE comparison
            x_pos = np.arange(len(metrics_df))
            axes[1, 1].bar(x_pos, metrics_df['MSE'], alpha=0.7, color=['red', 'green', 'orange'][:len(metrics_df)])
            axes[1, 1].set_xlabel('Models')
            axes[1, 1].set_ylabel('Mean Squared Error')
            axes[1, 1].set_title('Model Performance (MSE)')
            axes[1, 1].set_xticks(x_pos)
            axes[1, 1].set_xticklabels(metrics_df['Model'])
            axes[1, 1].grid(True, alpha=0.3)

            # Add text with metrics
            for i, row in metrics_df.iterrows():
                axes[1, 1].text(i, row['MSE'], f"MSE: {row['MSE']:.0f}\nMAE: {row['MAE']:.0f}",
                               ha='center', va='bottom', fontsize=8)
        else:
            axes[1, 1].text(0.5, 0.5, 'No successful models', ha='center', va='center',
                           transform=axes[1, 1].transAxes)

        plt.tight_layout()

        # Save plot
        safe_store_name = "".join(c for c in store_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        plt.savefig(f'{self.output_dir}/forecast_{safe_store_name.replace(" ", "_")}.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

        # Print model summary
        print(f"\nModel Performance Summary for {store_name}:")
        if model_metrics:
            for metric in model_metrics:
                print(f"{metric['Model']}: MSE={metric['MSE']:.2f}, MAE={metric['MAE']:.2f}, AIC={metric['AIC']:.2f}")
        else:
            print("No successful model fits")

    def _create_forecast_summary(self, forecast_results):
        """Create summary comparison of all stores and models"""
        print("\n=== FORECAST SUMMARY ===")

        # Collect all results
        summary_data = []
        for store, results in forecast_results.items():
            for model_name in ['ARIMA', 'SARIMA', 'SARIMAX']:
                if results.get(model_name) and results[model_name] is not None:
                    summary_data.append({
                        'Store': store,
                        'Model': model_name,
                        'MSE': results[model_name]['mse'],
                        'MAE': results[model_name]['mae'],
                        'AIC': results[model_name]['aic']
                    })

        if not summary_data:
            print("No successful forecasts to summarize")
            return

        summary_df = pd.DataFrame(summary_data)

        # Create summary visualizations
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle('Forecasting Models Performance Summary', fontsize=16, fontweight='bold')

        # Plot 1: MSE by model type
        mse_by_model = summary_df.groupby('Model')['MSE'].mean()
        mse_by_model.plot(kind='bar', ax=axes[0, 0], color=['red', 'green', 'orange'])
        axes[0, 0].set_title('Average MSE by Model Type')
        axes[0, 0].set_ylabel('Mean Squared Error')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # Plot 2: MAE by model type
        mae_by_model = summary_df.groupby('Model')['MAE'].mean()
        mae_by_model.plot(kind='bar', ax=axes[0, 1], color=['red', 'green', 'orange'])
        axes[0, 1].set_title('Average MAE by Model Type')
        axes[0, 1].set_ylabel('Mean Absolute Error')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # Plot 3: Best model per store
        best_models = summary_df.loc[summary_df.groupby('Store')['MSE'].idxmin()]
        model_counts = best_models['Model'].value_counts()
        model_counts.plot(kind='pie', ax=axes[1, 0], autopct='%1.1f%%')
        axes[1, 0].set_title('Best Model Distribution (by MSE)')

        # Plot 4: Performance distribution
        models = summary_df['Model'].unique()
        mse_data = [summary_df[summary_df['Model'] == model]['MSE'].values for model in models]
        axes[1, 1].boxplot(mse_data, labels=models)
        axes[1, 1].set_title('MSE Distribution by Model')
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/forecast_summary.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print summary statistics
        print("\nModel Performance Statistics:")
        print(summary_df.groupby('Model')[['MSE', 'MAE', 'AIC']].agg(['mean', 'std']).round(2))

        print("\nBest Model per Store (by MSE):")
        for _, row in best_models.iterrows():
            print(f"{row['Store']}: {row['Model']} (MSE: {row['MSE']:.2f})")

        # Save summary to CSV
        summary_df.to_csv(f'{self.output_dir}/forecast_summary.csv', index=False)
        best_models.to_csv(f'{self.output_dir}/best_models_per_store.csv', index=False)

    def run_complete_analysis(self):
        """Run the complete clustering and forecasting analysis"""
        print("Starting Complete Store Analytics...")
        print("="*50)

        # Run clustering analysis
        self.perform_clustering_analysis()

        # Run forecasting analysis
        forecast_results = self.perform_arima_forecasting()

        print("\n" + "="*50)
        print("Analysis Complete!")
        print(f"All plots and results saved to: {self.output_dir}")
        print("="*50)

        return forecast_results


# Main execution
if __name__ == "__main__":
    # Initialize the analytics
    analytics = StoreAnalytics('synthetic_store_data.csv')

    # Run complete analysis
    results = analytics.run_complete_analysis()
