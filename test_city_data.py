#!/usr/bin/env python3
"""
Test script for Indonesian city population data
"""

import csv

def analyze_city_data():
    """Analyze the generated city data"""
    
    print("=== INDONESIAN CITY DATA ANALYSIS ===")
    
    # Read CSV data
    cities = []
    with open('indonesia_cities_population.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        cities = list(reader)
    
    # Basic statistics
    total_cities = len(cities)
    total_population = sum(int(city['population_2024']) for city in cities)
    avg_population = total_population / total_cities if total_cities > 0 else 0
    
    print(f"Total cities: {total_cities}")
    print(f"Total urban population: {total_population:,}")
    print(f"Average city population: {avg_population:,.0f}")
    print()
    
    # Top 5 cities
    print("Top 5 cities by population:")
    sorted_cities = sorted(cities, key=lambda x: int(x['population_2024']), reverse=True)
    for i, city in enumerate(sorted_cities[:5], 1):
        pop = int(city['population_2024'])
        print(f"  {i}. {city['city_name']}: {pop:,} ({city['province']})")
    print()
    
    # Regional distribution
    print("Cities by region:")
    regions = {}
    for city in cities:
        region = city['region']
        if region not in regions:
            regions[region] = {'count': 0, 'population': 0}
        regions[region]['count'] += 1
        regions[region]['population'] += int(city['population_2024'])
    
    for region, stats in sorted(regions.items(), key=lambda x: x[1]['population'], reverse=True):
        print(f"  {region}: {stats['count']} cities, {stats['population']:,} people")
    print()
    
    # Data quality
    print("Data quality distribution:")
    quality_counts = {}
    for city in cities:
        quality = city['data_quality']
        quality_counts[quality] = quality_counts.get(quality, 0) + 1
    
    for quality, count in quality_counts.items():
        print(f"  {quality}: {count} cities")
    print()
    
    # City types
    print("City type distribution:")
    type_counts = {}
    for city in cities:
        city_type = city['city_type']
        type_counts[city_type] = type_counts.get(city_type, 0) + 1
    
    for city_type, count in type_counts.items():
        print(f"  {city_type}: {count} cities")

if __name__ == "__main__":
    analyze_city_data()
