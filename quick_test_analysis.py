import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.decomposition import PCA
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
import warnings
warnings.filterwarnings('ignore')
import os

# Set style for better plots
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)

def quick_analysis():
    """Quick test of clustering and ARIMA functionality"""
    
    # Load data
    df = pd.read_csv('synthetic_store_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    
    print("Data loaded successfully!")
    print(f"Total stores: {df['businessName'].nunique()}")
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    
    # Create output directory
    output_dir = 'output/arima'
    os.makedirs(output_dir, exist_ok=True)
    
    # === CLUSTERING ANALYSIS ===
    print("\n=== CLUSTERING ANALYSIS ===")
    
    # Create store summary
    store_summary = df.groupby('businessName').agg({
        'businessType': 'first',
        'isDigital': 'first',
        'hasWebsite': 'first',
        'sales': ['mean', 'std'],
        'customers': ['mean', 'std']
    }).round(2)
    
    # Flatten column names
    store_summary.columns = ['_'.join(col).strip() if col[1] else col[0] 
                           for col in store_summary.columns.values]
    store_summary.reset_index(inplace=True)
    
    # Encode categorical variables
    le_business = LabelEncoder()
    store_summary['businessType_encoded'] = le_business.fit_transform(store_summary['businessType_first'])
    
    # Select features for clustering
    clustering_features = ['businessType_encoded', 'isDigital_first', 'hasWebsite_first', 
                          'sales_mean', 'sales_std', 'customers_mean', 'customers_std']
    
    X = store_summary[clustering_features]
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply K-Means clustering
    kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
    store_summary['cluster'] = kmeans.fit_predict(X_scaled)
    
    # Visualize clusters using PCA
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_scaled)
    
    plt.figure(figsize=(12, 8))
    scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1], c=store_summary['cluster'], 
                         cmap='viridis', s=100, alpha=0.7)
    
    # Add store names as labels
    for i, store in enumerate(store_summary['businessName']):
        plt.annotate(store, (X_pca[i, 0], X_pca[i, 1]), 
                    xytext=(5, 5), textcoords='offset points', 
                    fontsize=8, alpha=0.8)
    
    plt.colorbar(scatter)
    plt.xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.2%} variance)')
    plt.ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.2%} variance)')
    plt.title('Store Clustering Results (K-Means with PCA)')
    plt.grid(True, alpha=0.3)
    plt.savefig(f'{output_dir}/quick_clustering_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print cluster characteristics
    cluster_analysis = store_summary.groupby('cluster').agg({
        'businessType_first': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
        'isDigital_first': 'mean',
        'hasWebsite_first': 'mean',
        'sales_mean': 'mean',
        'customers_mean': 'mean',
        'businessName': 'count'
    }).round(2)
    
    print("\nCluster Characteristics:")
    print(cluster_analysis)
    
    # === ARIMA ANALYSIS ===
    print("\n=== ARIMA FORECASTING ANALYSIS ===")
    
    # Test with first 3 stores only for quick analysis
    test_stores = df['businessName'].unique()[:3]
    
    for store in test_stores:
        print(f"\n--- Forecasting for {store} ---")
        
        # Get store data
        store_data = df[df['businessName'] == store].copy()
        store_data = store_data.sort_values('date')
        
        # Create time series (weekly aggregation for faster processing)
        ts_data = store_data.set_index('date')['sales'].resample('W').mean().ffill()
        
        # Check if we have enough data
        if len(ts_data) < 20:
            print(f"Not enough data for {store}")
            continue
        
        # Split data
        train_size = int(len(ts_data) * 0.8)
        train_data = ts_data[:train_size]
        test_data = ts_data[train_size:]
        
        # Check stationarity
        adf_result = adfuller(train_data.dropna())
        print(f"ADF Test p-value: {adf_result[1]:.4f}")
        
        try:
            # Fit simple ARIMA model
            model = ARIMA(train_data, order=(1, 1, 1))
            fitted_model = model.fit()
            
            # Generate forecasts
            forecast_steps = len(test_data)
            forecast = fitted_model.forecast(steps=forecast_steps)
            
            # Calculate metrics
            mse = np.mean((test_data - forecast) ** 2)
            mae = np.mean(np.abs(test_data - forecast))
            
            print(f"ARIMA Model - MSE: {mse:.2f}, MAE: {mae:.2f}")
            
            # Plot results
            plt.figure(figsize=(15, 8))
            
            # Plot time series and forecast
            plt.subplot(2, 1, 1)
            plt.plot(train_data.index, train_data.values, label='Training Data', color='blue')
            plt.plot(test_data.index, test_data.values, label='Actual Test Data', color='black', linewidth=2)
            plt.plot(test_data.index, forecast, label='ARIMA Forecast', color='red', linewidth=2, linestyle='--')
            plt.title(f'Sales Forecasting - {store}')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Plot residuals
            plt.subplot(2, 1, 2)
            residuals = test_data - forecast
            plt.plot(test_data.index, residuals, color='red', alpha=0.7)
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            plt.title('Forecast Residuals')
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # Save plot
            safe_store_name = "".join(c for c in store if c.isalnum() or c in (' ', '-', '_')).rstrip()
            plt.savefig(f'{output_dir}/quick_forecast_{safe_store_name.replace(" ", "_")}.png', 
                       dpi=300, bbox_inches='tight')
            plt.show()
            
        except Exception as e:
            print(f"ARIMA failed for {store}: {e}")
    
    print(f"\nQuick analysis complete! Results saved to: {output_dir}")

if __name__ == "__main__":
    quick_analysis()
