# Store Analytics: Clustering and ARIMA Forecasting

## Overview
This project implements comprehensive store analytics using clustering algorithms and ARIMA forecasting models based on the synthetic dataset generated from `generateDatasets.py`. The analysis provides insights into store performance patterns and future sales predictions.

## Dataset Characteristics
- **20 stores** across 10 different business types
- **Time series data** from 2020-2025 (6 years)
- **3,068 total records** with daily sales and customer data
- **Multiple features** including business type, digital presence, location, promotions, holidays, etc.

## Analysis Components

### 1. Clustering Analysis
The clustering analysis groups stores based on their characteristics using multiple algorithms:

#### Algorithms Implemented:
- **K-Means Clustering**: Optimal k=4 clusters identified using elbow method and silhouette analysis
- **Hierarchical Clustering**: Agglomerative clustering with Ward linkage
- **DBSCAN**: Density-based clustering for outlier detection
- **Decision Tree Analysis**: ID3-like analysis for feature importance

#### Key Clustering Results:
- **Cluster 0**: Education & Training stores (89% digital, 100% have websites)
- **Cluster 1**: Health & Beauty stores (25% digital, 100% have websites)
- **Cluster 2**: F&B stores (0% digital, 0% have websites)
- **Cluster 3**: Fashion & Lifestyle stores (40% digital, 100% have websites)

#### Generated Visualizations:
- `kmeans_optimization.png`: Elbow method and silhouette score analysis
- `kmeans_clusters_pca.png`: K-Means clustering results with PCA visualization
- `hierarchical_clusters_pca.png`: Hierarchical clustering results
- `dbscan_clusters_pca.png`: DBSCAN clustering results
- `decision_tree_analysis.png`: Decision tree for business type classification
- `*_cluster_characteristics.png`: Cluster characteristic analysis for each method

### 2. ARIMA Forecasting Models
Time series forecasting implemented for store sales prediction using multiple ARIMA variants:

#### Models Implemented:
- **ARIMA**: Auto-regressive Integrated Moving Average
- **SARIMA**: Seasonal ARIMA with seasonal components
- **SARIMAX**: SARIMA with external variables (promotions, holidays, customers)

#### Forecasting Process:
1. **Stationarity Testing**: Augmented Dickey-Fuller test
2. **Data Preprocessing**: Weekly/Monthly aggregation for stability
3. **Model Selection**: Grid search for optimal parameters
4. **Validation**: Train/test split with performance metrics
5. **Evaluation**: MSE, MAE, MAPE, and AIC comparison

#### Key Forecasting Results:
- **Average MAPE**: 48.4% across top-performing stores
- **Best Model**: SARIMA for stores with clear seasonal patterns
- **Stationarity**: Most time series achieve stationarity after differencing
- **Seasonal Patterns**: February shows highest sales, August shows lowest

#### Generated Forecasting Plots:
- `quick_forecast_*.png`: Individual store forecasting results
- `forecast_summary.png`: Model performance comparison
- `final_forecast_summary.csv`: Detailed forecasting metrics

## Key Business Insights

### 1. Digital Transformation Impact
- Digital stores show different performance patterns than traditional stores
- Website presence strongly correlates with business performance
- Education & Training sector leads in digital adoption (89%)

### 2. Business Type Performance
**Top Performers by Average Sales:**
1. Health & Beauty: 6,010,292 average sales
2. Digital & Technology: 5,804,683 average sales
3. F&B: 5,798,017 average sales

### 3. Seasonal Patterns
- **Peak Season**: February (5,944,146 average sales)
- **Low Season**: August (5,264,441 average sales)
- Clear seasonal variations suitable for inventory planning

### 4. Clustering Effectiveness
- Stores naturally group by business type and digital presence
- 4 distinct clusters identified with clear characteristics
- Silhouette scores indicate good cluster separation

## Technical Implementation

### Files Structure:
```
├── generateDatasets.py          # Original dataset schema
├── synthetic_store_data.csv     # Generated dataset
├── clustering_and_forecasting.py # Full analysis implementation
├── quick_test_analysis.py       # Quick testing version
├── optimized_store_analytics.py # Optimized implementation
├── analysis_summary.py          # Final summary report
└── output/arima/               # Generated plots and results
    ├── *.png                   # Visualization files
    └── *.csv                   # Summary data files
```

### Dependencies:
- pandas, numpy: Data manipulation
- matplotlib: Visualization
- scikit-learn: Clustering algorithms
- statsmodels: ARIMA forecasting
- warnings: Error handling

## Usage Instructions

### 1. Run Complete Analysis:
```bash
python clustering_and_forecasting.py
```

### 2. Run Quick Test (3 stores):
```bash
python quick_test_analysis.py
```

### 3. Run Optimized Analysis (5 stores):
```bash
python optimized_store_analytics.py
```

### 4. Generate Summary Report:
```bash
python analysis_summary.py
```

## Model Performance

### Clustering Metrics:
- **K-Means**: 4 clusters with clear business type separation
- **Hierarchical**: Similar groupings with slight variations
- **DBSCAN**: Identifies outliers and dense regions

### Forecasting Metrics:
- **MSE Range**: 3.96e12 to 5.11e12 (high variance due to sales magnitude)
- **MAE Range**: 1.73M to 1.78M (more interpretable error metric)
- **MAPE**: 25.53% to 76.16% (varies by store characteristics)

## Recommendations

### 1. Business Strategy:
- Focus digital transformation on traditional F&B stores
- Leverage seasonal patterns for inventory and marketing planning
- Implement cluster-specific management strategies

### 2. Forecasting Implementation:
- Use monthly aggregation for more stable predictions
- Apply SARIMA models for stores with clear seasonality
- Include external variables (promotions, holidays) for better accuracy

### 3. Future Enhancements:
- Implement ensemble forecasting methods
- Add more external variables (weather, economic indicators)
- Develop real-time forecasting dashboard
- Explore deep learning models for complex patterns

## Conclusion
The analysis successfully demonstrates the effectiveness of combining clustering and ARIMA forecasting for retail analytics. The generated insights provide actionable recommendations for store management, inventory planning, and strategic decision-making.

All visualizations and detailed results are saved in the `output/arima/` directory for further analysis and presentation.
