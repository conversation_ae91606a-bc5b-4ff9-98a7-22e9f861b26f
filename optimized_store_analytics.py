import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
import warnings
warnings.filterwarnings('ignore')
import os

# Set style for better plots
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)

class OptimizedStoreAnalytics:
    def __init__(self, csv_file):
        """Initialize the analytics class"""
        self.df = pd.read_csv(csv_file)
        self.prepare_data()
        self.output_dir = 'output/arima'
        os.makedirs(self.output_dir, exist_ok=True)

    def prepare_data(self):
        """Prepare and clean the data"""
        self.df['date'] = pd.to_datetime(self.df['date'])
        self.df['city'] = self.df['address'].str.split(',').str[-1].str.strip()

        # Create store summary for clustering
        self.store_summary = self.df.groupby('businessName').agg({
            'businessType': 'first',
            'isDigital': 'first',
            'hasWebsite': 'first',
            'city': 'first',
            'sales': ['mean', 'std', 'sum'],
            'customers': ['mean', 'std', 'sum'],
            'promo': 'mean'
        }).round(2)

        # Flatten column names
        self.store_summary.columns = ['_'.join(col).strip() if col[1] else col[0]
                                     for col in self.store_summary.columns.values]
        self.store_summary.reset_index(inplace=True)

        print("Data prepared successfully!")
        print(f"Total stores: {self.df['businessName'].nunique()}")
        print(f"Date range: {self.df['date'].min()} to {self.df['date'].max()}")

    def perform_clustering_analysis(self):
        """Perform clustering analysis with multiple algorithms"""
        print("\n=== CLUSTERING ANALYSIS ===")

        # Prepare features
        features_df = self.store_summary.copy()

        # Encode categorical variables
        le_business = LabelEncoder()
        le_city = LabelEncoder()

        features_df['businessType_encoded'] = le_business.fit_transform(features_df['businessType_first'])
        features_df['city_encoded'] = le_city.fit_transform(features_df['city_first'])

        # Select features for clustering
        clustering_features = [
            'businessType_encoded', 'isDigital_first', 'hasWebsite_first',
            'city_encoded', 'sales_mean', 'customers_mean', 'promo_mean'
        ]

        X = features_df[clustering_features]
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # Apply clustering algorithms
        self._apply_clustering_methods(X_scaled, features_df)

        # Decision tree analysis
        self._decision_tree_analysis(features_df, clustering_features)

    def _apply_clustering_methods(self, X_scaled, features_df):
        """Apply different clustering methods"""

        # K-Means clustering
        print("\n--- K-Means Clustering ---")
        kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
        features_df['kmeans_cluster'] = kmeans.fit_predict(X_scaled)

        # Hierarchical clustering
        print("--- Hierarchical Clustering ---")
        hierarchical = AgglomerativeClustering(n_clusters=4, linkage='ward')
        features_df['hierarchical_cluster'] = hierarchical.fit_predict(X_scaled)

        # DBSCAN clustering
        print("--- DBSCAN Clustering ---")
        dbscan = DBSCAN(eps=0.5, min_samples=2)
        features_df['dbscan_cluster'] = dbscan.fit_predict(X_scaled)

        # Visualize all clustering results
        self._visualize_clustering_results(X_scaled, features_df)

        # Analyze cluster characteristics
        for method in ['kmeans_cluster', 'hierarchical_cluster', 'dbscan_cluster']:
            self._analyze_cluster_characteristics(features_df, method)

    def _visualize_clustering_results(self, X_scaled, features_df):
        """Create comprehensive clustering visualizations"""

        # PCA for visualization
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)

        # Create subplot for all clustering methods
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('Store Clustering Analysis Results', fontsize=16, fontweight='bold')

        methods = ['kmeans_cluster', 'hierarchical_cluster', 'dbscan_cluster']
        titles = ['K-Means Clustering', 'Hierarchical Clustering', 'DBSCAN Clustering']
        cmaps = ['viridis', 'plasma', 'tab10']

        for i, (method, title, cmap) in enumerate(zip(methods, titles, cmaps)):
            row = i // 2
            col = i % 2

            scatter = axes[row, col].scatter(X_pca[:, 0], X_pca[:, 1],
                                           c=features_df[method],
                                           cmap=cmap, s=100, alpha=0.7)

            # Add store names as labels
            for j, store in enumerate(features_df['businessName']):
                axes[row, col].annotate(store, (X_pca[j, 0], X_pca[j, 1]),
                                      xytext=(5, 5), textcoords='offset points',
                                      fontsize=8, alpha=0.8)

            axes[row, col].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
            axes[row, col].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
            axes[row, col].set_title(title)
            axes[row, col].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[row, col])

        # Business type distribution
        business_counts = features_df['businessType_first'].value_counts()
        axes[1, 1].pie(business_counts.values, labels=business_counts.index, autopct='%1.1f%%')
        axes[1, 1].set_title('Business Type Distribution')

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/comprehensive_clustering_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _analyze_cluster_characteristics(self, features_df, cluster_col):
        """Analyze cluster characteristics"""
        cluster_analysis = features_df.groupby(cluster_col).agg({
            'businessType_first': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            'isDigital_first': 'mean',
            'hasWebsite_first': 'mean',
            'sales_mean': 'mean',
            'customers_mean': 'mean',
            'businessName': 'count'
        }).round(2)

        print(f"\n{cluster_col.replace('_', ' ').title()} Characteristics:")
        print(cluster_analysis)

    def _decision_tree_analysis(self, features_df, clustering_features):
        """Perform decision tree analysis for feature importance"""
        print("\n--- Decision Tree Analysis ---")

        X = features_df[clustering_features]
        y = features_df['businessType_first']

        dt = DecisionTreeClassifier(criterion='entropy', max_depth=4, random_state=42)
        dt.fit(X, y)

        # Feature importance plot
        feature_importance = pd.DataFrame({
            'feature': clustering_features,
            'importance': dt.feature_importances_
        }).sort_values('importance', ascending=False)

        plt.figure(figsize=(10, 6))
        plt.barh(feature_importance['feature'], feature_importance['importance'])
        plt.title('Feature Importance in Business Type Classification')
        plt.xlabel('Importance Score')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/optimized_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("Feature Importance:")
        print(feature_importance)

    def perform_arima_forecasting(self, max_stores=5):
        """Perform ARIMA forecasting for selected stores"""
        print(f"\n=== ARIMA FORECASTING ANALYSIS (Top {max_stores} stores) ===")

        # Select top stores by total sales
        top_stores = self.store_summary.nlargest(max_stores, 'sales_sum')['businessName'].values

        forecast_results = {}

        for store in top_stores:
            print(f"\n--- Forecasting for {store} ---")

            store_data = self.df[self.df['businessName'] == store].copy()
            store_data = store_data.sort_values('date')

            # Create weekly time series for faster processing
            ts_data = store_data.set_index('date')['sales'].resample('W').mean().ffill()

            if len(ts_data) < 20:
                print(f"Not enough data for {store}")
                continue

            # Perform forecasting
            results = self._forecast_store_sales(store, ts_data, store_data)
            forecast_results[store] = results

        # Create summary
        self._create_forecast_summary(forecast_results)

        return forecast_results

    def _forecast_store_sales(self, store_name, ts_data, store_data):
        """Forecast sales for a single store"""

        # Split data
        train_size = int(len(ts_data) * 0.8)
        train_data = ts_data[:train_size]
        test_data = ts_data[train_size:]

        # Check stationarity
        adf_result = adfuller(train_data.dropna())
        print(f"ADF Test p-value: {adf_result[1]:.4f}")

        results = {}

        # ARIMA Model
        try:
            arima_model = ARIMA(train_data, order=(1, 1, 1))
            fitted_arima = arima_model.fit()

            forecast_arima = fitted_arima.forecast(steps=len(test_data))
            mse_arima = np.mean((test_data - forecast_arima) ** 2)
            mae_arima = np.mean(np.abs(test_data - forecast_arima))

            results['ARIMA'] = {
                'forecast': forecast_arima,
                'mse': mse_arima,
                'mae': mae_arima,
                'aic': fitted_arima.aic
            }

            print(f"ARIMA - MSE: {mse_arima:.2f}, MAE: {mae_arima:.2f}")

        except Exception as e:
            print(f"ARIMA failed: {e}")
            results['ARIMA'] = None

        # SARIMA Model
        try:
            sarima_model = SARIMAX(train_data, order=(1, 1, 1), seasonal_order=(1, 1, 1, 4))
            fitted_sarima = sarima_model.fit(disp=False)

            forecast_sarima = fitted_sarima.forecast(steps=len(test_data))
            mse_sarima = np.mean((test_data - forecast_sarima) ** 2)
            mae_sarima = np.mean(np.abs(test_data - forecast_sarima))

            results['SARIMA'] = {
                'forecast': forecast_sarima,
                'mse': mse_sarima,
                'mae': mae_sarima,
                'aic': fitted_sarima.aic
            }

            print(f"SARIMA - MSE: {mse_sarima:.2f}, MAE: {mae_sarima:.2f}")

        except Exception as e:
            print(f"SARIMA failed: {e}")
            results['SARIMA'] = None

        # Create plots
        self._plot_store_forecasts(store_name, ts_data, train_data, test_data, results)

        return results

    def _plot_store_forecasts(self, store_name, ts_data, train_data, test_data, results):
        """Create forecast plots for a store"""

        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        fig.suptitle(f'Sales Forecasting Analysis - {store_name}', fontsize=16, fontweight='bold')

        # Plot 1: Time series overview
        axes[0, 0].plot(ts_data.index, ts_data.values, label='Full Time Series', alpha=0.7)
        axes[0, 0].axvline(x=train_data.index[-1], color='red', linestyle='--', alpha=0.7, label='Train/Test Split')
        axes[0, 0].set_title('Time Series Overview')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Seasonal decomposition (if possible)
        try:
            if len(ts_data) >= 8:  # Need at least 2 seasons for decomposition
                decomposition = seasonal_decompose(ts_data.dropna(), model='additive', period=4)
                axes[0, 1].plot(decomposition.trend, label='Trend', color='blue')
                axes[0, 1].set_title('Trend Component')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
            else:
                axes[0, 1].text(0.5, 0.5, 'Not enough data for decomposition',
                               ha='center', va='center', transform=axes[0, 1].transAxes)
        except:
            axes[0, 1].text(0.5, 0.5, 'Decomposition failed',
                           ha='center', va='center', transform=axes[0, 1].transAxes)

        # Plot 3: Model forecasts comparison
        axes[1, 0].plot(train_data.index, train_data.values, label='Training Data', color='blue', alpha=0.7)
        axes[1, 0].plot(test_data.index, test_data.values, label='Actual Test Data', color='black', linewidth=2)

        colors = ['red', 'green']
        for i, model_name in enumerate(['ARIMA', 'SARIMA']):
            if results.get(model_name) and results[model_name] is not None:
                forecast = results[model_name]['forecast']
                axes[1, 0].plot(test_data.index, forecast, label=f'{model_name} Forecast',
                               color=colors[i], linewidth=2, linestyle='--')

        axes[1, 0].set_title('Model Forecasts Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Model performance metrics
        model_metrics = []
        for model_name in ['ARIMA', 'SARIMA']:
            if results.get(model_name) and results[model_name] is not None:
                model_metrics.append({
                    'Model': model_name,
                    'MSE': results[model_name]['mse'],
                    'MAE': results[model_name]['mae']
                })

        if model_metrics:
            metrics_df = pd.DataFrame(model_metrics)
            x_pos = np.arange(len(metrics_df))

            # Create bar plot for MSE
            bars = axes[1, 1].bar(x_pos, metrics_df['MSE'], alpha=0.7, color=['red', 'green'][:len(metrics_df)])
            axes[1, 1].set_xlabel('Models')
            axes[1, 1].set_ylabel('Mean Squared Error')
            axes[1, 1].set_title('Model Performance (MSE)')
            axes[1, 1].set_xticks(x_pos)
            axes[1, 1].set_xticklabels(metrics_df['Model'])
            axes[1, 1].grid(True, alpha=0.3)

            # Add value labels on bars
            for i, (bar, row) in enumerate(zip(bars, metrics_df.itertuples())):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                               f'MSE: {row.MSE:.0f}\nMAE: {row.MAE:.0f}',
                               ha='center', va='bottom', fontsize=9)
        else:
            axes[1, 1].text(0.5, 0.5, 'No successful models',
                           ha='center', va='center', transform=axes[1, 1].transAxes)

        plt.tight_layout()

        # Save plot
        safe_store_name = "".join(c for c in store_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        plt.savefig(f'{self.output_dir}/optimized_forecast_{safe_store_name.replace(" ", "_")}.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def _create_forecast_summary(self, forecast_results):
        """Create summary of forecasting results"""
        print("\n=== FORECAST SUMMARY ===")

        summary_data = []
        for store, results in forecast_results.items():
            for model_name in ['ARIMA', 'SARIMA']:
                if results.get(model_name) and results[model_name] is not None:
                    summary_data.append({
                        'Store': store,
                        'Model': model_name,
                        'MSE': results[model_name]['mse'],
                        'MAE': results[model_name]['mae'],
                        'AIC': results[model_name]['aic']
                    })

        if not summary_data:
            print("No successful forecasts to summarize")
            return

        summary_df = pd.DataFrame(summary_data)

        # Create summary visualization
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle('Forecasting Models Performance Summary', fontsize=16, fontweight='bold')

        # Plot 1: Average MSE by model
        mse_by_model = summary_df.groupby('Model')['MSE'].mean()
        mse_by_model.plot(kind='bar', ax=axes[0], color=['red', 'green'])
        axes[0].set_title('Average MSE by Model Type')
        axes[0].set_ylabel('Mean Squared Error')
        axes[0].tick_params(axis='x', rotation=45)

        # Plot 2: Best model per store
        best_models = summary_df.loc[summary_df.groupby('Store')['MSE'].idxmin()]
        model_counts = best_models['Model'].value_counts()
        if len(model_counts) > 0:
            model_counts.plot(kind='pie', ax=axes[1], autopct='%1.1f%%')
            axes[1].set_title('Best Model Distribution (by MSE)')

        # Plot 3: MSE comparison by store
        pivot_df = summary_df.pivot(index='Store', columns='Model', values='MSE')
        pivot_df.plot(kind='bar', ax=axes[2], color=['red', 'green'])
        axes[2].set_title('MSE by Store and Model')
        axes[2].set_ylabel('Mean Squared Error')
        axes[2].tick_params(axis='x', rotation=45)
        axes[2].legend()

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/optimized_forecast_summary.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print summary statistics
        print("\nModel Performance Statistics:")
        print(summary_df.groupby('Model')[['MSE', 'MAE', 'AIC']].agg(['mean', 'std']).round(2))

        print("\nBest Model per Store (by MSE):")
        for _, row in best_models.iterrows():
            print(f"{row['Store']}: {row['Model']} (MSE: {row['MSE']:.2f})")

        # Save results
        summary_df.to_csv(f'{self.output_dir}/optimized_forecast_summary.csv', index=False)
        best_models.to_csv(f'{self.output_dir}/optimized_best_models.csv', index=False)

    def run_complete_analysis(self, max_forecast_stores=5):
        """Run the complete optimized analysis"""
        print("Starting Optimized Store Analytics...")
        print("="*60)

        # Run clustering analysis
        self.perform_clustering_analysis()

        # Run forecasting analysis
        forecast_results = self.perform_arima_forecasting(max_stores=max_forecast_stores)

        print("\n" + "="*60)
        print("Optimized Analysis Complete!")
        print(f"All plots and results saved to: {self.output_dir}")
        print("="*60)

        return forecast_results


# Main execution
if __name__ == "__main__":
    # Initialize the analytics
    analytics = OptimizedStoreAnalytics('synthetic_store_data.csv')

    # Run complete analysis (limit to top 5 stores for forecasting)
    results = analytics.run_complete_analysis(max_forecast_stores=5)
